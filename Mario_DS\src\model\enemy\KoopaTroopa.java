package model.enemy;

import views.ImageLoader;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public class KoopaTroopa extends Enemy implements Serializable {

	private transient BufferedImage rightImage;

	public KoopaTroopa(double x, double y, BufferedImage style) {
		super(x, y, style);
		setVelX(3);
	}

	@Override
	public void draw(Graphics g) {
		if (getVelX() > 0) {
			g.drawImage(rightImage, (int) getX(), (int) getY(), null);
		} else {
			super.draw(g);
		}
	}

	public void setRightImage(BufferedImage rightImage) {
		this.rightImage = rightImage;
	}

	private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();
		ImageLoader imageLoader = new ImageLoader();
		BufferedImage sprite = imageLoader.loadImage("/sprite.png");
		setStyle(imageLoader.getSubImage(sprite, 1, 3, 48, 64));
		this.rightImage = imageLoader.getSubImage(sprite, 4, 3, 48, 64);
	}
}
