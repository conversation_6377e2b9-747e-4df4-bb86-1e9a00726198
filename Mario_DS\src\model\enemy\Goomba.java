package model.enemy;

import views.ImageLoader;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public class <PERSON>omba extends Enemy implements Serializable {

	private transient BufferedImage rightImage;

	public Goomba(double x, double y, BufferedImage style) {
		super(x, y, style);
		setVelX(3);
	}

	@Override
	public void draw(Graphics g) {
		if (getVelX() > 0) {
			g.drawImage(rightImage, (int) getX(), (int) getY(), null);
		} else
			super.draw(g);
	}

	public void setRightImage(BufferedImage rightImage) {
		this.rightImage = rightImage;
	}

	private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();
		ImageLoader imageLoader = new ImageLoader();
		BufferedImage sprite = imageLoader.loadImage("/sprite.png");
		setStyle(imageLoader.getSubImage(sprite, 2, 4, 48, 48));
		this.rightImage = imageLoader.getSubImage(sprite, 5, 4, 48, 48);
	}

}
