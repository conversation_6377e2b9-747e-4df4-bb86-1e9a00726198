package manager.network.tool;

import manager.network.NetworkManager;

import java.util.Timer;
import java.util.TimerTask;

public class HeartbeatManager {

	private final NetworkManager networkManager;
	private final Timer heartbeatTimer;
	private long lastHeartbeat;

	private static final long HEARTBEAT_INTERVAL = 200; // 200ms
	private static final long TIMEOUT = 60000; // 60 seconds

	public HeartbeatManager(NetworkManager networkManager) {
		this.networkManager = networkManager;
		this.heartbeatTimer = new Timer("HeartbeatTimer", true);
	}

	public void start() {
		lastHeartbeat = System.currentTimeMillis();
		heartbeatTimer.scheduleAtFixedRate(new TimerTask() {
			@Override
			public void run() {
				if (System.currentTimeMillis() - lastHeartbeat > TIMEOUT) {
					System.err.println("Connection timed out.");
					networkManager.cleanup();
					heartbeatTimer.cancel();
				} else {
					networkManager.sendHeartbeat();
				}
			}
		}, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL);
	}

	public void stop() {
		heartbeatTimer.cancel();
	}

	public void onHeartbeatReceived() {
		lastHeartbeat = System.currentTimeMillis();
	}
}
