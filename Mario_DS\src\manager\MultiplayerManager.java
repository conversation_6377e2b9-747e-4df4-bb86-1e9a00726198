package manager;

import java.awt.image.BufferedImage;

import manager.network.NetworkManager;
import manager.network.message.GameState;
import manager.network.message.InputMessage;
import model.hero.Mario;
import model.hero.MarioForm;
import views.Animation;
import views.ImageLoader;

/**
 * Manages multiplayer functionality across different game modes.
 * Coordinates input handling, player management, and network communication.
 */
public class MultiplayerManager {

	private GameMode currentMode;
	private NetworkManager networkManager;
	private GameEngine gameEngine;

	// Player management
	private boolean player1Active;
	private boolean player2Active;
	private String localPlayerId;
	private String remotePlayerId;
	private int selectedMultiplayerOption = 0;

	public MultiplayerManager(GameEngine gameEngine) {
		this.gameEngine = gameEngine;
		this.currentMode = GameMode.LOCAL_SINGLE_PLAYER;
		this.player1Active = true;
		this.player2Active = false;
		this.localPlayerId = "mario";
		this.remotePlayerId = "mario2";
	}

	/**
	 * Set the current game mode and configure the multiplayer system accordingly
	 *
	 * @param mode The new game mode to set
	 */
	public void setGameMode(GameMode mode) {
		this.currentMode = mode;
		configurePlayersForMode();

		if (mode.isNetworkMode() && networkManager == null) {
			networkManager = new NetworkManager(this);
		}
	}

	/**
	 * Configure which players are active based on the current game mode
	 */
	private void configurePlayersForMode() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				player1Active = true;
				player2Active = false;
				break;

			case LOCAL_MULTIPLAYER:
				player1Active = true;
				player2Active = true;
				break;

			case NETWORK_HOST:
				player1Active = true;
				player2Active = true; // Remote player will be controlled via network
				localPlayerId = "mario";
				remotePlayerId = "mario2";
				break;

			case NETWORK_CLIENT:
				player1Active = false; // Host controls mario
				player2Active = true; // Client controls mario2
				localPlayerId = "mario2";
				remotePlayerId = "mario";
				break;
		}
	}

	/**
	 * Handle input based on the current game mode and player assignment
	 *
	 * @param action   The button action to process
	 * @param playerId The player ID ("mario" or "mario2")
	 */
	public void handleInput(ButtonAction action, String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				}
				break;

			case LOCAL_MULTIPLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				} else if (playerId.equals("mario2")) {
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_HOST:
				if (playerId.equals(localPlayerId)) {
					gameEngine.receiveInputMario(action);
				} else if (playerId.equals(remotePlayerId)) {
					// Handle remote player input received from network
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_CLIENT:
				if (playerId.equals(localPlayerId)) {
					// Client-side prediction
					gameEngine.receiveInputMario2(action);
					// Send input to host via network
					if (networkManager != null) {
						networkManager.sendPlayerInput(action, localPlayerId);
					}
				} else if (playerId.equals(remotePlayerId)) {
					gameEngine.receiveInputMario(action);
				}
				break;
		}
	}

	/**
	 * Handle input from local keyboard/controls
	 *
	 * @param action    The button action from local input
	 * @param isPlayer1 True if input is from player 1 controls, false for player 2
	 */
	public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
		String playerId = isPlayer1 ? "mario" : "mario2";

		// Only process input if the player is locally controlled
		if ((isPlayer1 && isPlayerLocallyControlled("mario")) ||
				(!isPlayer1 && isPlayerLocallyControlled("mario2"))) {
			handleInput(action, playerId);
		}
	}

	/**
	 * Check if a player is controlled locally (not via network)
	 *
	 * @param playerId The player ID to check
	 * @return true if the player is controlled locally
	 */
	public boolean isPlayerLocallyControlled(String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return playerId.equals("mario");

			case LOCAL_MULTIPLAYER:
				return true; // Both players are local

			case NETWORK_HOST:
				return playerId.equals(localPlayerId);

			case NETWORK_CLIENT:
				return playerId.equals(localPlayerId);

			default:
				return false;
		}
	}

	/**
	 * Check if a player should be active/visible in the current mode
	 *
	 * @param playerId The player ID to check
	 * @return true if the player should be active
	 */
	public boolean isPlayerActive(String playerId) {
		if (playerId.equals("mario")) {
			return player1Active;
		} else if (playerId.equals("mario2")) {
			return player2Active;
		}
		return false;
	}

	/**
	 * Start hosting a network game
	 *
	 * @param port The port to host on
	 * @return true if hosting started successfully
	 */
	public boolean startHosting(int port) {
		if (networkManager != null) {
			return networkManager.startServer(port);
		}
		return false;
	}

	/**
	 * Connect to a network host
	 *
	 * @param hostIP The IP address of the host
	 * @param port   The port to connect to
	 * @return true if connection was successful
	 */
	public boolean connectToHost(String hostIP, int port) {
		if (networkManager != null) {
			return networkManager.connectToServer(hostIP, port);
		}
		return false;
	}

	/**
	 * Process a game state update received from the network
	 *
	 * @param gameState The game state data
	 */
	public void processNetworkGameState(Object gameState) {
		if (currentMode == GameMode.NETWORK_CLIENT && networkManager != null) {
			// Apply the authoritative game state from the host
			networkManager.applyGameState(gameState);
		}
	}

	/**
	 * Get the current game mode
	 *
	 * @return The current GameMode
	 */
	public GameMode getCurrentMode() {
		return currentMode;
	}

	/**
	 * Get the network manager instance
	 *
	 * @return The NetworkManager, or null if not in network mode
	 */
	public NetworkManager getNetworkManager() {
		return networkManager;
	}

	/**
	 * Cleanup resources when shutting down multiplayer
	 */
	public void cleanup() {
		if (networkManager != null) {
			networkManager.cleanup();
			networkManager = null;
		}
	}

	public int getSelectedMultiplayerOption() {
		return selectedMultiplayerOption;
	}

	public void changeSelectedMultiplayerOption(boolean up) {
		if (up) {
			selectedMultiplayerOption = (selectedMultiplayerOption - 1 + 3) % 3;
		} else {
			selectedMultiplayerOption = (selectedMultiplayerOption + 1) % 3;
		}
	}

	public void onPlayerConnected(String playerId) {
		System.out.println("Player connected: " + playerId);
	}

	public void onGameStateReceived(GameState gameState) {
		if (currentMode.isNetworkMode() && !networkManager.isServer()) {
			MapManager mapManager = gameEngine.getMapManager();
			var map = mapManager.getMap();

			if (map == null) {
				gameEngine.createMap(gameState.getMapPath(), gameState.getMap().getRemainingTime());
				gameEngine.setGameStatus(GameStatus.RUNNING);
				return;
			}

			gameEngine.setGameStatus(gameState.getGameStatus());

			// Sync map objects
			map.setBricks(gameState.getBricks());
			map.setEnemies(gameState.getEnemies());
			map.setPrizes(gameState.getPrizes());

			// Server reconciliation for the local player
			if (localPlayerId.equals("mario2")) {
				var mario2 = gameEngine.getMario2();
				mario2.setLocation(gameState.getPlayer2Position().getX(),
						gameState.getPlayer2Position().getY());

				if (gameState.isPlayer2IsFire() && !mario2.getMarioForm().isFire()) {
					ImageLoader imageLoader = new ImageLoader();

					BufferedImage[] leftFrames = imageLoader.getLeftFrames2(MarioForm.FIRE);
					BufferedImage[] rightFrames = imageLoader.getRightFrames2(MarioForm.FIRE);

					Animation animation = new Animation(leftFrames, rightFrames);
					MarioForm newForm = new MarioForm(animation, true, true, "mario2");

					mario2.setMarioForm(newForm);
					mario2.setDimension(48, 96);
				}

				if (gameState.isPlayer2IsSuper() && !mario2.getMarioForm().isSuper()) {
					ImageLoader imageLoader = new ImageLoader();

					BufferedImage[] leftFrames = imageLoader.getLeftFrames2(MarioForm.SUPER);
					BufferedImage[] rightFrames = imageLoader.getRightFrames2(MarioForm.SUPER);

					Animation animation = new Animation(leftFrames, rightFrames);
					MarioForm newForm = new MarioForm(animation, true, false, "mario2");

					mario2.setMarioForm(newForm);
					mario2.setDimension(48, 96);
				}

				if (!gameState.isPlayer2IsSuper() && mario2.getMarioForm().isSuper()
						|| !gameState.isPlayer2IsFire() && mario2.getMarioForm().isFire()) {
					ImageLoader imageLoader = new ImageLoader();

					BufferedImage[] leftFrames = imageLoader.getLeftFrames2(MarioForm.SMALL);
					BufferedImage[] rightFrames = imageLoader.getRightFrames2(MarioForm.SMALL);

					Animation animation = new Animation(leftFrames, rightFrames);
					MarioForm newForm = new MarioForm(animation, false, false, "mario2");

					mario2.setMarioForm(newForm);
					mario2.setDimension(48, 48);
				}

				mario2.setRemainingLives(gameState.getPlayer2Lives());
				mario2.setPoints(gameState.getPlayer2Score());
			}

			// Interpolate the remote player's position for smoother movement
			Mario remotePlayer = gameEngine.getMario();
			int x = (int) (remotePlayer.getX() * 0.5 + gameState.getPlayer1Position().getX() * 0.5);
			int y = (int) (remotePlayer.getY() * 0.5 + gameState.getPlayer1Position().getY() * 0.5);

			remotePlayer.setLocation(x, y);

			if (gameState.isPlayer1IsFire() && !remotePlayer.getMarioForm().isFire()) {
				ImageLoader imageLoader = new ImageLoader();

				BufferedImage[] leftFrames = imageLoader.getLeftFrames(MarioForm.FIRE);
				BufferedImage[] rightFrames = imageLoader.getRightFrames(MarioForm.FIRE);

				Animation animation = new Animation(leftFrames, rightFrames);
				MarioForm newForm = new MarioForm(animation, true, true, "mario");

				remotePlayer.setMarioForm(newForm);
				remotePlayer.setDimension(48, 96);
			}

			if (gameState.isPlayer1IsSuper() && !remotePlayer.getMarioForm().isSuper()) {
				ImageLoader imageLoader = new ImageLoader();

				BufferedImage[] leftFrames = imageLoader.getLeftFrames(MarioForm.SUPER);
				BufferedImage[] rightFrames = imageLoader.getRightFrames(MarioForm.SUPER);

				Animation animation = new Animation(leftFrames, rightFrames);
				MarioForm newForm = new MarioForm(animation, true, false, "mario");

				remotePlayer.setMarioForm(newForm);
				remotePlayer.setDimension(48, 96);
			}

			if (!gameState.isPlayer1IsSuper() && remotePlayer.getMarioForm().isSuper()
					|| !gameState.isPlayer1IsFire() && remotePlayer.getMarioForm().isFire()) {
				ImageLoader imageLoader = new ImageLoader();

				BufferedImage[] leftFrames = imageLoader.getLeftFrames(MarioForm.SMALL);
				BufferedImage[] rightFrames = imageLoader.getRightFrames(MarioForm.SMALL);

				Animation animation = new Animation(leftFrames, rightFrames);
				MarioForm newForm = new MarioForm(animation, false, false, "mario1");

				remotePlayer.setMarioForm(newForm);
				remotePlayer.setDimension(48, 48);
			}

			remotePlayer.setRemainingLives(gameState.getPlayer1Lives());
			remotePlayer.setPoints(gameState.getPlayer1Score());

		}
	}

	public void onInputReceived(InputMessage inputMessage) {
		if (currentMode.isNetworkMode() && networkManager.isServer()) {
			handleInput(inputMessage.getAction(), inputMessage.getPlayerId());
		}
	}

	public void onDisconnection() {
		System.out.println("Disconnected from network game.");
		// setGameMode(GameMode.LOCAL_SINGLE_PLAYER);
		// gameEngine.setGameStatus(GameStatus.START_SCREEN);
		if (currentMode.isNetworkMode()) {
			gameEngine.setGameStatus(GameStatus.RECONNECTING);
			attemptReconnection();
		} else {
			setGameMode(GameMode.LOCAL_SINGLE_PLAYER);
			gameEngine.setGameStatus(GameStatus.START_SCREEN);
		}
	}

	private void attemptReconnection() {
		new Thread(() -> {
			while (gameEngine.getGameStatus() == GameStatus.RECONNECTING) {
				System.out.println("Attempting to reconnect...");
				boolean success = false;
				if (currentMode == GameMode.NETWORK_HOST) {
					success = networkManager.startServer(4444);
				} else if (currentMode == GameMode.NETWORK_CLIENT) {
					// Need to store host IP somewhere accessible
					// For now, assuming a fixed IP for demonstration
					success = networkManager.connectToServer("localhost", 4444);
				}

				if (success) {
					System.out.println("Reconnection successful!");
					gameEngine.setGameStatus(GameStatus.RUNNING);
					break;
				}

				try {
					Thread.sleep(5000); // Wait 5 seconds before retrying
				} catch (InterruptedException e) {
					Thread.currentThread().interrupt();
					break;
				}
			}
		}).start();
	}

	public void update() {
		if (currentMode == GameMode.NETWORK_HOST && networkManager != null && networkManager.isServer()) {
			Mario mario = gameEngine.getMario();
			Mario mario2 = gameEngine.getMario2();
			MapManager mapManager = gameEngine.getMapManager();

			if (mario != null && mario2 != null && mapManager.getMap() != null) {
				String mapPath = mapManager.getMapPath();
				var map = mapManager.getMap();

				GameState gameState = new GameState(mario.getLocation(), mario.getMarioForm().isSuper(),
						mario.getMarioForm().isFire(), mario.getPoints(), mario.getRemainingLives(), mario.getToRight(),
						mario2.getLocation(), mario2.getMarioForm().isSuper(),
						mario2.getMarioForm().isFire(), mario2.getPoints(), mario2.getRemainingLives(), mario2.getToRight(),
						mapPath,
						map.getBricks(), map.getEnemies(), map.getRevealedPrizes(), gameEngine.getGameStatus(),
						gameEngine.getMapManager().getMap());

				networkManager.sendGameState(gameState);
			}
		}
	}

	/**
	 * Get a display-friendly name for the current game mode
	 *
	 * @return String representation of the current mode
	 */
	public String getCurrentModeDisplayName() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return "Single Player";
			case LOCAL_MULTIPLAYER:
				return "Local Multiplayer";
			case NETWORK_HOST:
				return "Network Host";
			case NETWORK_CLIENT:
				return "Network Client";
			default:
				return "Unknown";
		}
	}
}
