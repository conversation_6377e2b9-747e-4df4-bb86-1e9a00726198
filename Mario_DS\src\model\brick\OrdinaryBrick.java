package model.brick;

import manager.GameEngine;
import manager.MapManager;
import model.GameObject;
import model.Map;
import model.hero.Mario;

import model.prize.Prize;
import views.Animation;
import views.ImageLoader;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public class OrdinaryBrick extends Brick implements Serializable {

	private transient Animation animation;
	private boolean breaking;
	private int frames;

	public OrdinaryBrick(double x, double y, BufferedImage style) {
		super(x, y, style);
		setBreakable(true);
		setEmpty(true);

		setAnimation();
		breaking = false;
		frames = animation.getLeftFrames().length;
	}

	private void setAnimation() {
		ImageLoader imageLoader = new ImageLoader();
		BufferedImage[] leftFrames = imageLoader.getBrickFrames();

		animation = new Animation(leftFrames, leftFrames);
	}

	@Override
	public void breakBrick(GameEngine engine, Mario mario) {
		MapManager manager = engine.getMapManager();

		if (!mario.isSuper())
			return;

		breaking = true;
		manager.addRevealedBrick(this);

		double newX = getX() - 27, newY = getY() - 27;
		setLocation(newX, newY);

		return;
	}

	@Override
	public void breakBrick2(GameEngine engine, Mario mario2) {
		MapManager manager = engine.getMapManager();

		if (!mario2.isSuper())
			return;

		breaking = true;
		manager.addRevealedBrick(this);

		double newX = getX() - 27, newY = getY() - 27;
		setLocation(newX, newY);

		return;
	}

	public int getFrames() {
		return frames;
	}

	public void animate() {
		if (breaking) {
			setStyle(animation.animate(3, true));
			frames--;
		}
	}

	private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();
		setAnimation();
		ImageLoader imageLoader = new ImageLoader();
		BufferedImage sprite = imageLoader.loadImage("/sprite.png");
		setStyle(imageLoader.getSubImage(sprite, 1, 1, 48, 48));
	}
}
