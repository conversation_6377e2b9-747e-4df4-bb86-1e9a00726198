package manager.network.message;

import manager.ButtonAction;
import java.io.Serializable;

public class InputMessage implements Serializable {
	private static final long serialVersionUID = 1L;

	private final ButtonAction action;
	private final String playerId;
	private final long timestamp;

	public InputMessage(ButtonAction action, String playerId) {
		this.action = action;
		this.playerId = playerId;
		this.timestamp = System.currentTimeMillis();
	}

	public ButtonAction getAction() {
		return action;
	}

	public String getPlayerId() {
		return playerId;
	}

	public long getTimestamp() {
		return timestamp;
	}
}
