package manager.network.message;

import java.awt.Point;
import java.io.Serializable;
import java.util.ArrayList;

import manager.GameStatus;
import model.Map;
import model.brick.Brick;
import model.enemy.Enemy;
import model.prize.Prize;

public class GameState implements Serializable {
	private static final long serialVersionUID = 1L;

	private final Point player1Position;
	private final boolean player1IsSuper;
	private final boolean player1IsFire;
	private final int player1Score;
	private final int player1Lives;
	private final boolean player1IsRight;

	private final Point player2Position;
	private final boolean player2IsSuper;
	private final boolean player2IsFire;
	private final int player2Score;
	private final int player2Lives;
	private final boolean player2IsRight;

	private final String mapPath;
	private final long timestamp;

	private final ArrayList<Brick> bricks;
	private final ArrayList<Enemy> enemies;
	private final ArrayList<Prize> prizes;
	private final GameStatus gameStatus;
	private final Map map;

	public GameState(Point player1Position, boolean player1IsSuper, boolean player1IsFire, int player1Score,
			int player1Lives, boolean player1IsRight,
			Point player2Position, boolean player2IsSuper, boolean player2IsFire, int player2Score, int player2Lives,
			boolean player2IsRight,
			String mapPath, ArrayList<Brick> bricks, ArrayList<Enemy> enemies, ArrayList<Prize> prizes,
			GameStatus gameStatus, Map map) {
		this.player1Position = player1Position;
		this.player1IsSuper = player1IsSuper;
		this.player1IsFire = player1IsFire;
		this.player1Score = player1Score;
		this.player1Lives = player1Lives;
		this.player1IsRight = player1IsRight;

		this.player2Position = player2Position;
		this.player2IsSuper = player2IsSuper;
		this.player2IsFire = player2IsFire;
		this.player2Score = player2Score;
		this.player2Lives = player2Lives;
		this.player2IsRight = player2IsRight;

		this.mapPath = mapPath;
		this.bricks = bricks;
		this.enemies = enemies;
		this.prizes = prizes;
		this.gameStatus = gameStatus;
		this.map = map;

		this.timestamp = System.currentTimeMillis();
	}

	public String getMapPath() {
		return mapPath;
	}

	public Point getPlayer1Position() {
		return player1Position;
	}

	public Point getPlayer2Position() {
		return player2Position;
	}

	public boolean isPlayer1IsSuper() {
		return player1IsSuper;
	}

	public boolean isPlayer1IsFire() {
		return player1IsFire;
	}

	public boolean isPlayer2IsSuper() {
		return player2IsSuper;
	}

	public boolean isPlayer2IsFire() {
		return player2IsFire;
	}

	public boolean isPlayer2IsRight() {
		return player2IsRight;
	}

	public boolean isPlayer1IsRight() {
		return player1IsRight;
	}

	public int getPlayer1Score() {
		return player1Score;
	}

	public int getPlayer1Lives() {
		return player1Lives;
	}

	public int getPlayer2Score() {
		return player2Score;
	}

	public int getPlayer2Lives() {
		return player2Lives;
	}

	public GameStatus getGameStatus() {
		return gameStatus;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public ArrayList<Brick> getBricks() {
		return bricks;
	}

	public ArrayList<Enemy> getEnemies() {
		return enemies;
	}

	public ArrayList<Prize> getPrizes() {
		return prizes;
	}

	public Map getMap() {
		return map;
	}
}
