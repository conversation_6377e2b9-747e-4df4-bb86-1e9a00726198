package manager.network.tool;

public class LatencyManager {

	private long rtt;
	private long lastRequestTime;

	public LatencyManager() {
		this.rtt = 0;
	}

	public void recordRequest() {
		lastRequestTime = System.currentTimeMillis();
	}

	public void recordResponse() {
		rtt = System.currentTimeMillis() - lastRequestTime;
	}

	public long getLatency() {
		return rtt / 2;
	}

	public long getRtt() {
		return rtt;
	}
}
